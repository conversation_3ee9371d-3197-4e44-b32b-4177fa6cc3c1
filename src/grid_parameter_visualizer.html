<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H3网格参数可视化调节器</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/h3-js@4.1.0/dist/h3-js.umd.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .parameter-panel {
            width: 400px;
            background: white;
            box-shadow: 2px 0 15px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            height: 100%;
            width: 100%;
        }
        
        .header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.5em;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .level-config {
            padding: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .level-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .level-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .level-1 { background: #e74c3c; }
        .level-2 { background: #f39c12; }
        .level-3 { background: #27ae60; }
        
        .param-group {
            margin-bottom: 20px;
        }
        
        .param-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .param-value {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #495057;
        }
        
        .param-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            outline: none;
            appearance: none;
            cursor: pointer;
        }
        
        .param-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        
        .param-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }
        
        .param-info {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .statistics {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stat-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
        }
        
        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .cost-estimate {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .cost-value {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .cost-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .actions {
            padding: 20px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
            margin-top: 10px;
        }
        
        .info-box {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            max-width: 300px;
            z-index: 1001;
        }
        
        .center-marker {
            width: 10px;
            height: 10px;
            background: #2c3e50;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="parameter-panel">
            <div class="header">
                <h1>H3网格参数调节器</h1>
                <p>实时调整H3六边形网格参数，优化扫描策略</p>
            </div>
            
            <!-- Level 1 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-1"></div>
                    Level 1 H3网格 (红色)
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>H3分辨率</span>
                        <span class="param-value" id="h3-res-level1-value">5</span>
                    </div>
                    <input type="range" class="param-slider" id="h3-res-level1"
                           min="3" max="7" step="1" value="5">
                    <div class="param-info">H3分辨率级别，数值越大网格越小越密集 (3=~1000km², 5=~50km²)</div>
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="search-radius-level1-value">5000 m</span>
                    </div>
                    <input type="range" class="param-slider" id="search-radius-level1"
                           min="2000" max="10000" step="500" value="5000">
                    <div class="param-info">每个Level 1网格的搜索半径（米）</div>
                </div>
            </div>
            
            <!-- Level 2 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-2"></div>
                    Level 2 H3网格 (橙色)
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>H3分辨率</span>
                        <span class="param-value" id="h3-res-level2-value">7</span>
                    </div>
                    <input type="range" class="param-slider" id="h3-res-level2"
                           min="5" max="9" step="1" value="7">
                    <div class="param-info">Level 2 H3分辨率，应大于Level 1 (7=~5km², 8=~0.7km²)</div>
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="search-radius-level2-value">1000 m</span>
                    </div>
                    <input type="range" class="param-slider" id="search-radius-level2"
                           min="500" max="2000" step="100" value="1000">
                    <div class="param-info">Level 2网格搜索半径（米）</div>
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>触发阈值</span>
                        <span class="param-value" id="recursion-trigger-count-value">20 个</span>
                    </div>
                    <input type="range" class="param-slider" id="recursion-trigger-count"
                           min="10" max="50" step="5" value="20">
                    <div class="param-info">发现多少个地点时触发Level 3增强扫描</div>
                </div>
            </div>
            
            <!-- Level 3 配置 -->
            <div class="level-config">
                <div class="level-title">
                    <div class="level-icon level-3"></div>
                    Level 3 H3网格 (绿色)
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>H3分辨率</span>
                        <span class="param-value" id="h3-res-level3-value">8</span>
                    </div>
                    <input type="range" class="param-slider" id="h3-res-level3"
                           min="7" max="10" step="1" value="8">
                    <div class="param-info">Level 3 H3分辨率，用于热点区域密集扫描 (8=~0.7km², 9=~0.1km²)</div>
                </div>

                <div class="param-group">
                    <div class="param-label">
                        <span>搜索半径</span>
                        <span class="param-value" id="search-radius-level3-value">500 m</span>
                    </div>
                    <input type="range" class="param-slider" id="search-radius-level3"
                           min="200" max="1000" step="50" value="500">
                    <div class="param-info">Level 3网格搜索半径（米）</div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="statistics">
                <div class="stat-title">预估统计</div>
                
                <div class="stat-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="total-grids">0</div>
                        <div class="stat-label">总网格数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="coverage-ratio">0%</div>
                        <div class="stat-label">覆盖率</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="level1-count">0</div>
                        <div class="stat-label">Level 1</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="level2-count">0</div>
                        <div class="stat-label">Level 2</div>
                    </div>
                </div>
                
                <div class="cost-estimate">
                    <div class="cost-value" id="estimated-cost">$0</div>
                    <div class="cost-label">预估成本 (含Level 3)</div>
                </div>
                
                <div style="font-size: 0.8em; color: #666; margin-top: 10px; text-align: center;">
                    🌍 使用Haversine公式精确计算
                </div>
                
                <div id="cost-warning" class="warning" style="display: none;">
                    ⚠️ 成本可能超出预算限制
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="actions">
                <button class="btn btn-primary" onclick="exportConfig()">导出配置文件</button>
                <button class="btn btn-secondary" onclick="resetToDefaults()">重置为默认值</button>
                <button class="btn btn-secondary" onclick="loadRecommended()">加载推荐配置</button>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="info-box">
                <h4>精确地理计算</h4>
                <p><strong>🌍 Haversine公式：</strong>精确计算地球表面距离</p>
                <p><strong>📐 地球曲率：</strong>自动修正不同纬度的经度距离</p>
                <p><strong>🎯 完全覆盖：</strong>确保边界区域无遗漏</p>
                <p><strong>💡 建议：</strong>搜索半径略小于网格间距，避免过度重叠</p>
                <hr style="margin: 10px 0; border: 1px solid #eee;">
                                 <p style="font-size: 0.8em; color: #666;">
                     <strong>算法优势：</strong><br>
                     • 真实地理距离计算<br>
                     • 适用于任何纬度地区<br>
                     • 边界覆盖零遗漏<br><br>
                     <strong>💡 提示：</strong><br>
                     点击地图任意位置查看精确距离
                 </p>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // ========== 地球曲面精确计算工具函数 ==========
        
        /**
         * 使用Haversine公式计算地球表面两点间的实际距离(km)
         */
        function calculateHaversineDistance(lat1, lng1, lat2, lng2) {
            const R = 6371; // 地球半径(km)
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }
        
        /**
         * 根据起点、方位角和距离计算终点坐标
         */
        function calculateDestinationPoint(lat, lng, bearing, distance) {
            const R = 6371; // 地球半径(km)
            const δ = distance / R; // 角距离
            const φ1 = lat * Math.PI / 180; // 起点纬度(弧度)
            const λ1 = lng * Math.PI / 180; // 起点经度(弧度)
            const θ = bearing * Math.PI / 180; // 方位角(弧度)
            
            const φ2 = Math.asin(Math.sin(φ1) * Math.cos(δ) + 
                                Math.cos(φ1) * Math.sin(δ) * Math.cos(θ));
            const λ2 = λ1 + Math.atan2(Math.sin(θ) * Math.sin(δ) * Math.cos(φ1),
                                      Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2));
            
            return {
                lat: φ2 * 180 / Math.PI,
                lng: ((λ2 * 180 / Math.PI) + 540) % 360 - 180 // 标准化经度
            };
        }
        
        // 旧的网格生成函数已移除，现在使用H3网格系统

        // 默认参数配置 - 与ProjectConfig保持一致，基于H3网格外接圆半径计算
        const defaultConfig = {
            centerLat: 34.0522,
            centerLng: -118.2437,
            scanRadius: 25, // km
            h3ResLevel1: 5,
            h3ResLevel2: 7,
            h3ResLevel3: 8,
            searchRadiusLevel1: 12000, // 12km for Level 1 (H3 res 5, 外接圆半径~9.8km + 重叠缓冲)
            searchRadiusLevel2: 1800,  // 1.8km for Level 2 (H3 res 7, 外接圆半径~1.4km + 重叠缓冲)
            searchRadiusLevel3: 700,   // 0.7km for Level 3 (H3 res 8, 外接圆半径~0.53km + 重叠缓冲)
            recursionTriggerCount: 20,
            apiCostPerCall: 0.032,
            maxBudget: 200
        };

        let config = { ...defaultConfig };
        let map;
        let gridLayers = {
            level1: L.layerGroup(),
            level2: L.layerGroup(),
            level3: L.layerGroup()
        };

        // H3网格辅助函数
        function createCirclePolygon(lat, lng, radiusKm, numPoints = 32) {
            const points = [];
            const earthRadius = 6371; // km

            for (let i = 0; i < numPoints; i++) {
                const angle = (i * 2 * Math.PI) / numPoints;
                const deltaLat = (radiusKm / earthRadius) * Math.cos(angle);
                const deltaLng = (radiusKm / earthRadius) * Math.sin(angle) / Math.cos(lat * Math.PI / 180);

                points.push([lat + deltaLat * 180 / Math.PI, lng + deltaLng * 180 / Math.PI]);
            }

            return points;
        }

        function generateH3Grid(centerLat, centerLng, radiusKm, h3Resolution) {
            try {
                // 创建扫描区域的多边形
                const boundary = createCirclePolygon(centerLat, centerLng, radiusKm);

                // 使用H3生成网格
                const h3Indexes = h3.polygonToCells(boundary, h3Resolution);

                const gridCells = [];
                h3Indexes.forEach(h3Index => {
                    const [lat, lng] = h3.cellToLatLng(h3Index);
                    const boundary = h3.cellToBoundary(h3Index);

                    gridCells.push({
                        h3Index: h3Index,
                        lat: lat,
                        lng: lng,
                        boundary: boundary,
                        resolution: h3Resolution
                    });
                });

                return gridCells;
            } catch (error) {
                console.error('H3网格生成失败:', error);
                return [];
            }
        }

        // 初始化地图
        function initMap() {
            map = L.map('map').setView([config.centerLat, config.centerLng], 10);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // 添加中心点标记
            L.marker([config.centerLat, config.centerLng], {
                icon: L.divIcon({
                    className: 'center-marker',
                    iconSize: [10, 10]
                })
            }).addTo(map);

            // 添加扫描边界
            L.circle([config.centerLat, config.centerLng], {
                radius: config.scanRadius * 1000,
                fillColor: 'blue',
                fillOpacity: 0.1,
                color: 'blue',
                weight: 2
            }).addTo(map);

            // 添加图层到地图
            Object.values(gridLayers).forEach(layer => layer.addTo(map));
            
            // 生成初始网格
            generateGrids();
        }

        // 生成H3网格
        function generateGrids() {
            // 清除现有网格
            Object.values(gridLayers).forEach(layer => layer.clearLayers());

            // 生成 Level 1 H3网格
            const level1Grids = generateLevel1H3Grid();

            // 生成 Level 2 H3网格 (模拟部分触发)
            const level2Grids = generateLevel2H3Grid(level1Grids.slice(0, Math.min(3, level1Grids.length)));

            // 更新统计信息
            updateStatistics(level1Grids.length, level2Grids.length);
        }

        function generateLevel1H3Grid() {
            const gridCells = generateH3Grid(
                config.centerLat,
                config.centerLng,
                config.scanRadius,
                config.h3ResLevel1
            );

            const grids = [];

            gridCells.forEach(cell => {
                grids.push({
                    lat: cell.lat,
                    lng: cell.lng,
                    level: 1,
                    h3Index: cell.h3Index,
                    boundary: cell.boundary
                });

                // 绘制H3六边形网格
                const polygon = L.polygon(cell.boundary, {
                    fillColor: '#e74c3c',
                    fillOpacity: 0.2,
                    color: '#e74c3c',
                    weight: 2
                }).addTo(gridLayers.level1);

                // 添加搜索半径圆圈
                L.circle([cell.lat, cell.lng], {
                    radius: config.searchRadiusLevel1,
                    fillColor: '#e74c3c',
                    fillOpacity: 0.1,
                    color: '#e74c3c',
                    weight: 1,
                    dashArray: '5, 5'
                }).addTo(gridLayers.level1);
            });

            return grids;
        }

        function generateLevel2H3Grid(triggerGrids) {
            const grids = [];

            triggerGrids.forEach(parentGrid => {
                try {
                    // 生成子网格
                    const childIndexes = h3.cellToChildren(parentGrid.h3Index, config.h3ResLevel2);

                    childIndexes.forEach(childIndex => {
                        const [lat, lng] = h3.cellToLatLng(childIndex);
                        const boundary = h3.cellToBoundary(childIndex);

                        grids.push({
                            lat: lat,
                            lng: lng,
                            level: 2,
                            h3Index: childIndex,
                            boundary: boundary,
                            parent: parentGrid
                        });

                        // 绘制Level 2 H3网格
                        L.polygon(boundary, {
                            fillColor: '#f39c12',
                            fillOpacity: 0.3,
                            color: '#f39c12',
                            weight: 1
                        }).addTo(gridLayers.level2);

                        // 添加搜索半径圆圈
                        L.circle([lat, lng], {
                            radius: config.searchRadiusLevel2,
                            fillColor: '#f39c12',
                            fillOpacity: 0.1,
                            color: '#f39c12',
                            weight: 1,
                            dashArray: '3, 3'
                        }).addTo(gridLayers.level2);
                    });
                } catch (error) {
                    console.error('生成Level 2网格失败:', error);
                }
            });

            return grids;
        }

        // 更新统计信息
        function updateStatistics(level1Count, level2Count) {
            document.getElementById('level1-count').textContent = level1Count;
            document.getElementById('level2-count').textContent = level2Count;
            document.getElementById('total-grids').textContent = level1Count + level2Count;

            // H3网格覆盖率计算
            const totalScanArea = Math.PI * config.scanRadius * config.scanRadius;

            // 基于H3分辨率估算覆盖率
            const h3CellArea = getH3CellAreaKm2(config.h3ResLevel1);
            const searchRadiusKm = config.searchRadiusLevel1 / 1000;
            const searchArea = Math.PI * searchRadiusKm * searchRadiusKm;

            // 计算覆盖效率
            let effectiveCoverage;
            if (searchArea >= h3CellArea) {
                // 搜索半径覆盖整个网格单元
                effectiveCoverage = Math.min(100, (level1Count * searchArea / totalScanArea) * 100);
            } else {
                // 部分覆盖
                effectiveCoverage = Math.min(100, (level1Count * searchArea / totalScanArea) * 80);
            }

            document.getElementById('coverage-ratio').textContent = effectiveCoverage.toFixed(1) + '%';

            // 计算预估成本（包含可能的Level 3）
            const baseCost = (level1Count + level2Count) * config.apiCostPerCall;

            // 基于H3分辨率估算Level 3网格数量
            const avgChildrenPerLevel2 = Math.pow(7, config.h3ResLevel3 - config.h3ResLevel2); // H3平均每个父网格有7个子网格
            const estimatedLevel3 = Math.ceil(level2Count * 0.2 * avgChildrenPerLevel2);
            const totalEstimatedCost = (level1Count + level2Count + estimatedLevel3) * config.apiCostPerCall;

            document.getElementById('estimated-cost').textContent = '$' + totalEstimatedCost.toFixed(2);

            // 显示成本警告
            const warningElement = document.getElementById('cost-warning');
            if (totalEstimatedCost > config.maxBudget) {
                warningElement.style.display = 'block';
                warningElement.textContent = `⚠️ 预估成本($${totalEstimatedCost.toFixed(2)})可能超出预算($${config.maxBudget})`;
            } else {
                warningElement.style.display = 'none';
            }
        }

        // H3网格单元面积估算（平方公里）
        function getH3CellAreaKm2(resolution) {
            // H3分辨率对应的大致面积（平方公里）
            const areas = {
                0: 4250546.8, 1: 607220.9, 2: 86745.9, 3: 12392.3,
                4: 1770.3, 5: 252.9, 6: 36.1, 7: 5.2,
                8: 0.74, 9: 0.11, 10: 0.015
            };
            return areas[resolution] || 1.0;
        }

        // 参数更新处理
        function setupParameterHandlers() {
            // Level 1 H3参数
            document.getElementById('h3-res-level1').addEventListener('input', function(e) {
                config.h3ResLevel1 = parseInt(e.target.value);
                document.getElementById('h3-res-level1-value').textContent = config.h3ResLevel1;
                generateGrids();
            });

            document.getElementById('search-radius-level1').addEventListener('input', function(e) {
                config.searchRadiusLevel1 = parseInt(e.target.value);
                document.getElementById('search-radius-level1-value').textContent = config.searchRadiusLevel1 + ' m';
                generateGrids();
            });

            // Level 2 H3参数
            document.getElementById('h3-res-level2').addEventListener('input', function(e) {
                config.h3ResLevel2 = parseInt(e.target.value);
                document.getElementById('h3-res-level2-value').textContent = config.h3ResLevel2;
                generateGrids();
            });

            document.getElementById('fine-radius').addEventListener('input', function(e) {
                config.fineRadius = parseFloat(e.target.value);
                document.getElementById('fine-radius-value').textContent = config.fineRadius + ' km';
                generateGrids();
            });

            // Enhanced 参数
            document.getElementById('enhanced-spacing').addEventListener('input', function(e) {
                config.enhancedSpacing = parseFloat(e.target.value);
                document.getElementById('enhanced-spacing-value').textContent = config.enhancedSpacing + ' km';
                generateGrids();
            });

            document.getElementById('enhanced-radius').addEventListener('input', function(e) {
                config.enhancedRadius = parseFloat(e.target.value);
                document.getElementById('enhanced-radius-value').textContent = config.enhancedRadius + ' km';
                generateGrids();
            });

            // 触发阈值
            document.getElementById('trigger-threshold').addEventListener('input', function(e) {
                config.triggerThreshold = parseInt(e.target.value);
                document.getElementById('trigger-threshold-value').textContent = config.triggerThreshold + ' 个';
            });
        }

        // 导出H3配置
        function exportConfig() {
            // 当前时间戳
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

            const configData = {
                // H3网格参数 - 与ProjectConfig保持一致
                h3_res_level1: config.h3ResLevel1,
                h3_res_level2: config.h3ResLevel2,
                h3_res_level3: config.h3ResLevel3,

                // 搜索半径（米）
                search_radius_level1: config.searchRadiusLevel1,
                search_radius_level2: config.searchRadiusLevel2,
                search_radius_level3: config.searchRadiusLevel3,

                // 递归参数
                recursion_trigger_count: config.recursionTriggerCount,

                // API配置
                api_cost_per_call: config.apiCostPerCall,
                max_budget: config.maxBudget,

                // 元信息
                _metadata: {
                    generated_at: new Date().toISOString(),
                    tool: "H3 Grid Parameter Visualizer",
                    center_coordinates: [config.centerLat, config.centerLng],
                    scan_radius_km: config.scanRadius,
                    notes: "使用H3六边形网格系统生成的参数配置，与ProjectConfig格式兼容"
                },

                // 计算说明
                _calculations: {
                    estimated_level1_grids: document.getElementById('level1-count').textContent,
                    estimated_level2_grids: document.getElementById('level2-count').textContent,
                    coverage_ratio: document.getElementById('coverage-ratio').textContent,
                    estimated_cost: document.getElementById('estimated-cost').textContent,
                    h3_cell_areas: {
                        level1: getH3CellAreaKm2(config.h3ResLevel1) + " km²",
                        level2: getH3CellAreaKm2(config.h3ResLevel2) + " km²",
                        level3: getH3CellAreaKm2(config.h3ResLevel3) + " km²"
                    }
                }
            };

            const dataStr = JSON.stringify(configData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `h3_grid_config_${timestamp}.json`;
            link.click();
            
            // 显示导出成功提示
            const originalText = document.querySelector('.btn-primary').textContent;
            const btn = document.querySelector('.btn-primary');
            btn.textContent = '✅ 配置已导出';
            btn.style.background = '#27ae60';
            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '';
            }, 2000);
        }

        // 重置为默认值
        function resetToDefaults() {
            config = { ...defaultConfig };
            updateUIValues();
            generateGrids();
        }

        // 加载推荐H3配置
        function loadRecommended() {
            // 基于城市规模的推荐H3配置 - 与ProjectConfig保持一致
            const recommended = {
                h3ResLevel1: 5,      // ~252 km² per cell
                h3ResLevel2: 7,      // ~5.2 km² per cell
                h3ResLevel3: 8,      // ~0.74 km² per cell
                searchRadiusLevel1: 5000,  // 5km search radius - 与ProjectConfig一致
                searchRadiusLevel2: 1000,  // 1km search radius - 与ProjectConfig一致
                searchRadiusLevel3: 500,   // 500m search radius - 与ProjectConfig一致
                recursionTriggerCount: 20  // 与ProjectConfig一致
            };

            Object.assign(config, recommended);
            updateUIValues();
            generateGrids();
        }

        // 更新UI值
        function updateUIValues() {
            document.getElementById('h3-res-level1').value = config.h3ResLevel1;
            document.getElementById('h3-res-level1-value').textContent = config.h3ResLevel1;
            document.getElementById('search-radius-level1').value = config.searchRadiusLevel1;
            document.getElementById('search-radius-level1-value').textContent = config.searchRadiusLevel1 + ' m';

            document.getElementById('h3-res-level2').value = config.h3ResLevel2;
            document.getElementById('h3-res-level2-value').textContent = config.h3ResLevel2;
            document.getElementById('search-radius-level2').value = config.searchRadiusLevel2;
            document.getElementById('search-radius-level2-value').textContent = config.searchRadiusLevel2 + ' m';

            document.getElementById('h3-res-level3').value = config.h3ResLevel3;
            document.getElementById('h3-res-level3-value').textContent = config.h3ResLevel3;
            document.getElementById('search-radius-level3').value = config.searchRadiusLevel3;
            document.getElementById('search-radius-level3-value').textContent = config.searchRadiusLevel3 + ' m';

            document.getElementById('recursion-trigger-count').value = config.recursionTriggerCount;
            document.getElementById('recursion-trigger-count-value').textContent = config.recursionTriggerCount + ' 个';
        }

        // 添加地图点击事件，显示距离验证
        function addMapClickHandler() {
            map.on('click', function(e) {
                const clickedLat = e.latlng.lat;
                const clickedLng = e.latlng.lng;
                
                // 计算到中心点的距离
                const distanceToCenter = calculateHaversineDistance(
                    config.centerLat, config.centerLng, 
                    clickedLat, clickedLng
                );
                
                // 显示临时弹窗
                const popup = L.popup()
                    .setLatLng([clickedLat, clickedLng])
                    .setContent(`
                        <div style="text-align: center;">
                            <strong>📍 距离验证</strong><br>
                            到中心点距离: <strong>${distanceToCenter.toFixed(2)} km</strong><br>
                            <small>使用Haversine公式计算</small>
                        </div>
                    `)
                    .openOn(map);
                
                // 3秒后自动关闭
                setTimeout(() => {
                    map.closePopup(popup);
                }, 3000);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            setupParameterHandlers();
            addMapClickHandler();
        });
    </script>
</body>
</html> 